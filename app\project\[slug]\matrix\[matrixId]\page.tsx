'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AppLayout from '@/components/AppLayout';
import KeywordResearchTable from '@/components/KeywordResearchTable';
import ContentMatrixTable from '@/components/ContentMatrixTable';
import ResultSummaryDashboard from '@/components/ResultSummaryDashboard';
import { Project, Matrix } from '@/types';
import { useAuth } from '@/context/AuthContext';
import { generateMockWebhookData } from '@/utils/mockData';

interface MatrixPageProps {
  params: {
    slug: string;
    matrixId: string;
  };
}

export default function MatrixPage({ params }: MatrixPageProps) {
  const [project, setProject] = useState<Project | null>(null);
  const [matrix, setMatrix] = useState<Matrix | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!user) {
      router.push('/login-with-otp');
      return;
    }

    const fetchData = async () => {
      try {
        setIsLoading(true);
        
        // Mock project data based on slug
        const mockProject: Project = {
          id: '1',
          name: params.slug.split('-').map(word => 
            word.charAt(0).toUpperCase() + word.slice(1)
          ).join(' '),
          description: `A comprehensive project for ${params.slug.replace(/-/g, ' ')} content strategy`,
          userId: user.id,
          niche: 'Technology',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        // Generate mock matrix data based on matrixId
        let mockMatrix: Matrix;
        let keyword = 'ai tools for productivity'; // Default keyword

        // Extract keyword from matrix ID if it contains keyword info
        if (params.matrixId.includes('-')) {
          keyword = params.matrixId.split('-').slice(0, -1).join(' ');
        }

        const mockData = generateMockWebhookData(keyword, 'United States', 'English');
        mockMatrix = {
          id: params.matrixId,
          projectId: mockProject.id,
          userId: user.id,
          mainKeyword: keyword,
          filename: `${keyword.toLowerCase().replace(/\s+/g, '-')}-matrix.json`,
          location: 'United States',
          language: 'English',
          keywordResearch: mockData.keywordResearch,
          contentMatrix: mockData.contentMatrix,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        setProject(mockProject);
        setMatrix(mockMatrix);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load matrix data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [user, params.slug, params.matrixId, router]);

  if (isLoading) {
    return (
      <AppLayout>
        <div className="space-y-8">
          <div className="bg-white shadow-md rounded-lg p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded mb-4 w-1/3"></div>
              <div className="h-4 bg-gray-200 rounded mb-6 w-1/2"></div>
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-8">
        {project && matrix ? (
          <>
            <div className="bg-white shadow-md rounded-lg p-6">
              <div className="flex justify-between items-start mb-6">
                <div>
                  <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                    <Link href="/dashboard" className="hover:text-gray-700">Dashboard</Link>
                    <span>/</span>
                    <Link href={`/project/${params.slug}`} className="hover:text-gray-700">{project.name}</Link>
                    <span>/</span>
                    <span className="text-gray-900">Matrix Results</span>
                  </nav>
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">
                    {matrix.mainKeyword} - Content Matrix
                  </h1>
                  <p className="text-gray-600">
                    Generated on {new Date(matrix.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <div className="flex space-x-3">
                  <Link
                    href={`/project/${params.slug}`}
                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                    </svg>
                    Back to Project
                  </Link>
                  <button
                    onClick={() => {
                      const dataStr = JSON.stringify({
                        project: project.name,
                        matrix: matrix,
                        exportDate: new Date().toISOString()
                      }, null, 2);
                      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
                      const exportFileDefaultName = matrix.filename;
                      const linkElement = document.createElement('a');
                      linkElement.setAttribute('href', dataUri);
                      linkElement.setAttribute('download', exportFileDefaultName);
                      linkElement.click();
                    }}
                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                    Export
                  </button>
                </div>
              </div>
            </div>

            {error && (
              <div className="bg-red-50 border-l-4 border-red-500 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            )}

            <ResultSummaryDashboard
              keywordData={matrix.keywordResearch}
              contentData={matrix.contentMatrix}
              mainKeyword={matrix.mainKeyword}
              location={matrix.location}
              language={matrix.language}
            />

            <KeywordResearchTable
              data={matrix.keywordResearch}
              isLoading={false}
            />

            <ContentMatrixTable
              data={matrix.contentMatrix}
              isLoading={false}
            />
          </>
        ) : (
          <div className="bg-white shadow-md rounded-lg p-6 text-center">
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Matrix not found</h2>
            <p className="text-gray-600 mb-6">The matrix you're looking for doesn't exist or you don't have access to it.</p>
            <Link
              href={`/project/${params.slug}`}
              className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
            >
              Back to Project
            </Link>
          </div>
        )}
      </div>
    </AppLayout>
  );
}
