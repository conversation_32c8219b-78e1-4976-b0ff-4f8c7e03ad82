'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AppLayout from '@/components/AppLayout';
import MatrixList from '@/components/MatrixList';
import KeywordForm from '@/components/KeywordForm';
import { Project, Matrix, FormData } from '@/types';
import { useAuth } from '@/context/AuthContext';
import { generateMockWebhookData } from '@/utils/mockData';

interface ProjectPageProps {
  params: {
    slug: string;
  };
}

export default function ProjectPage({ params }: ProjectPageProps) {
  const router = useRouter();
  const { user, isLoading: authLoading } = useAuth();
  const [project, setProject] = useState<Project | null>(null);
  const [matrices, setMatrices] = useState<Matrix[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showMatrixGenerator, setShowMatrixGenerator] = useState(false);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login-with-otp');
    }
  }, [user, authLoading, router]);

  // Fetch project and matrices
  useEffect(() => {
    const fetchProjectData = async () => {
      if (!user) return;

      setIsLoading(true);
      try {
        // In a real app, you would fetch from your API
        // For demo purposes, we'll use mock data

        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 800));

        // Fetch project by slug
        const projectResponse = await fetch(`/api/projects/slug/${params.slug}`);

        if (!projectResponse.ok) {
          const errorData = await projectResponse.json();
          throw new Error(errorData.error || 'Failed to fetch project');
        }

        const projectData = await projectResponse.json();
        const foundProject = projectData.project;

        if (!foundProject) {
          setError('Project not found');
          setIsLoading(false);
          return;
        }

        // Fetch matrices for the project
        const matricesResponse = await fetch(`/api/matrices/project/${foundProject._id}`);

        if (!matricesResponse.ok) {
          const errorData = await matricesResponse.json();
          throw new Error(errorData.error || 'Failed to fetch matrices');
        }

        const matricesData = await matricesResponse.json();
        const projectMatrices = matricesData.matrices || [];

        setProject(foundProject);
        setMatrices(projectMatrices);
      } catch (err) {
        console.error('Error fetching project data:', err);
        setError('Failed to load project data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProjectData();
  }, [user, params.slug]);

  // Handle matrix creation
  const handleFormSubmit = async (formData: FormData) => {
    if (!user || !project) return;

    setIsLoading(true);
    setError(null);

    try {
      // For demo purposes, we'll generate mock data
      const mockData = generateMockWebhookData(
        formData.mainKeyword,
        formData.location,
        formData.language
      );

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Create a new matrix with keyword-based ID
      const keywordSlug = formData.mainKeyword.toLowerCase().replace(/\s+/g, '-');
      const matrixId = `${keywordSlug}-${Date.now()}`;

      const newMatrix: Matrix = {
        id: matrixId,
        projectId: project.id,
        userId: user.id,
        mainKeyword: formData.mainKeyword,
        filename: `${keywordSlug}-matrix.json`,
        location: formData.location,
        language: formData.language,
        keywordResearch: mockData.keywordResearch,
        contentMatrix: mockData.contentMatrix,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      setMatrices(prevMatrices => [...prevMatrices, newMatrix]);
      setShowMatrixGenerator(false);

      // Redirect to the new matrix page
      const projectSlug = project.name.toLowerCase().replace(/\s+/g, '-');
      router.push(`/project/${projectSlug}/matrix/${matrixId}`);

    } catch (err) {
      console.error('Error creating matrix:', err);
      setError('Failed to create matrix. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AppLayout>
      <div className="space-y-8">
        {isLoading ? (
          <div className="bg-white shadow-md rounded-lg p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded mb-4 w-1/3"></div>
              <div className="h-4 bg-gray-200 rounded mb-6 w-1/2"></div>
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
          </div>
        ) : project ? (
          <>
            <div className="bg-white shadow-md rounded-lg p-6">
              <div className="flex justify-between items-start">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">{project.name}</h1>
                  {project.description && (
                    <p className="text-gray-600 mb-2">{project.description}</p>
                  )}
                  <div className="flex flex-col space-y-2">
                    {project.niche && (
                      <div className="text-sm text-gray-600 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                        </svg>
                        <span>Niche: {project.niche}</span>
                      </div>
                    )}

                    {project.website && (
                      <a
                        href={project.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-indigo-600 hover:text-indigo-800 flex items-center"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z" clipRule="evenodd" />
                        </svg>
                        {project.website}
                      </a>
                    )}
                  </div>
                </div>
                <Link
                  href={`/project/${params.slug}/edit`}
                  className="text-sm text-gray-600 hover:text-gray-900 flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                  </svg>
                  Edit Project
                </Link>
              </div>
            </div>

            {error && (
              <div className="bg-red-50 border-l-4 border-red-500 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {showMatrixGenerator ? (
              <div className="bg-white shadow-md rounded-lg p-6 mb-8">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold text-gray-800">Create New Matrix</h2>
                  <button
                    onClick={() => setShowMatrixGenerator(false)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
                <p className="text-gray-600 mb-4">
                  Enter your main keyword to generate a comprehensive content matrix.
                </p>
                <KeywordForm onSubmit={handleFormSubmit} />
              </div>
            ) : (
              <MatrixList matrices={matrices} projectId={project.id} projectName={project.name} />
            )}
          </>
        ) : (
          <div className="bg-white shadow-md rounded-lg p-6 text-center">
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Project not found</h2>
            <p className="text-gray-600 mb-6">The project you're looking for doesn't exist or you don't have access to it.</p>
            <Link
              href="/dashboard"
              className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
            >
              Back to Dashboard
            </Link>
          </div>
        )}
      </div>
    </AppLayout>
  );
}
