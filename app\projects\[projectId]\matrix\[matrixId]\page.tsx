'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AppLayout from '@/components/AppLayout';
import KeywordResearchTable from '@/components/KeywordResearchTable';
import ContentMatrixTable from '@/components/ContentMatrixTable';
import { Project, Matrix } from '@/types';
import { useAuth } from '@/context/AuthContext';
import { generateMockWebhookData } from '@/utils/mockData';

interface MatrixPageProps {
  params: {
    projectId: string;
    matrixId: string;
  };
}

export default function MatrixPage({ params }: MatrixPageProps) {
  const router = useRouter();
  const { user, isLoading: authLoading } = useAuth();
  const [project, setProject] = useState<Project | null>(null);
  const [matrix, setMatrix] = useState<Matrix | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login-with-otp');
    }
  }, [user, authLoading, router]);

  // Fetch project and matrix data
  useEffect(() => {
    const fetchData = async () => {
      if (!user) return;

      setIsLoading(true);
      try {
        // In a real app, you would fetch from your API
        // For demo purposes, we'll use mock data

        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 800));

        // Mock project data
        const mockProject: Project = {
          id: params.projectId,
          name: params.projectId === '1' ? 'My Tech Blog' : 'Fitness Website',
          description: params.projectId === '1'
            ? 'A blog about the latest technology trends and reviews'
            : 'Website focused on fitness tips and workout routines',
          userId: user.id,
          website: params.projectId === '1' ? 'https://techblog.example.com' : 'https://fitness.example.com',
          niche: params.projectId === '1' ? 'Technology' : 'Health & Fitness',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        // Generate mock matrix data
        let mockMatrix: Matrix;

        if (params.matrixId === '101') {
          const mockData = generateMockWebhookData('artificial intelligence', 'United States', 'English');
          mockMatrix = {
            id: params.matrixId,
            projectId: params.projectId,
            userId: user.id,
            mainKeyword: 'artificial intelligence',
            filename: 'artificial-intelligence-matrix.json',
            location: 'United States',
            language: 'English',
            keywordResearch: mockData.keywordResearch,
            contentMatrix: mockData.contentMatrix,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };
        } else if (params.matrixId === '102') {
          const mockData = generateMockWebhookData('machine learning', 'United States', 'English');
          mockMatrix = {
            id: params.matrixId,
            projectId: params.projectId,
            userId: user.id,
            mainKeyword: 'machine learning',
            filename: 'machine-learning-matrix.json',
            location: 'United States',
            language: 'English',
            keywordResearch: mockData.keywordResearch,
            contentMatrix: mockData.contentMatrix,
            createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          };
        } else {
          // For any other ID, generate random data
          const mockData = generateMockWebhookData('digital marketing', 'United States', 'English');
          mockMatrix = {
            id: params.matrixId,
            projectId: params.projectId,
            userId: user.id,
            mainKeyword: 'digital marketing',
            filename: 'digital-marketing-matrix.json',
            location: 'United States',
            language: 'English',
            keywordResearch: mockData.keywordResearch,
            contentMatrix: mockData.contentMatrix,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };
        }

        setProject(mockProject);
        setMatrix(mockMatrix);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load matrix data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [user, params.projectId, params.matrixId]);

  return (
    <AppLayout>
      <div className="space-y-8">
        {isLoading ? (
          <div className="bg-white shadow-md rounded-lg p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded mb-4 w-1/3"></div>
              <div className="h-4 bg-gray-200 rounded mb-6 w-1/2"></div>
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
          </div>
        ) : matrix && project ? (
          <>
            <div className="bg-white shadow-md rounded-lg p-6">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                <div>
                  <div className="flex items-center mb-2">
                    <Link href={`/projects/${project.id}`} className="text-indigo-600 hover:text-indigo-800 mr-2">
                      {project.name}
                    </Link>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 mx-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="font-semibold text-gray-700">Matrix</span>
                  </div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">{matrix.mainKeyword}</h1>
                  <div className="flex flex-wrap items-center text-sm text-gray-500 mb-4">
                    <span className="mr-4 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                      </svg>
                      {matrix.location}
                    </span>
                    <span className="mr-4 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M7 2a1 1 0 011 1v1h3a1 1 0 110 2H9.20l.8 9.1a1 1 0 01-1 1.1H4a1 1 0 01-1-1.1L3.8 6H2a1 1 0 110-2h3V3a1 1 0 011-1h1zm0 2v2H5V4h2zm7 6a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                      {matrix.language}
                    </span>
                    <span className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                      </svg>
                      {new Date(matrix.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
                <div className="flex space-x-2 mt-4 sm:mt-0">
                  <button
                    onClick={() => {
                      // Download matrix data as JSON
                      const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(matrix));
                      const downloadAnchorNode = document.createElement('a');
                      downloadAnchorNode.setAttribute("href", dataStr);
                      downloadAnchorNode.setAttribute("download", matrix.filename);
                      document.body.appendChild(downloadAnchorNode);
                      downloadAnchorNode.click();
                      downloadAnchorNode.remove();
                    }}
                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                    Export
                  </button>
                </div>
              </div>
            </div>

            {error && (
              <div className="bg-red-50 border-l-4 border-red-500 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            )}

            <KeywordResearchTable
              data={matrix.keywordResearch}
              isLoading={false}
            />

            <ContentMatrixTable
              data={matrix.contentMatrix}
              isLoading={false}
            />
          </>
        ) : (
          <div className="bg-white shadow-md rounded-lg p-6 text-center">
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Matrix not found</h2>
            <p className="text-gray-600 mb-6">The matrix you're looking for doesn't exist or you don't have access to it.</p>
            <Link
              href={`/projects/${params.projectId}`}
              className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
            >
              Back to Project
            </Link>
          </div>
        )}
      </div>
    </AppLayout>
  );
}
