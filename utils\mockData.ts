import { KeywordResearchItem, ContentMatrixItem, WebhookData } from '@/types';

// Function to generate mock keyword research data
export const generateMockKeywordResearch = (
  mainKeyword: string,
  count: number = 10
): KeywordResearchItem[] => {
  const types = ['Informational', 'Transactional', 'Navigational', 'Commercial'];
  const searchIntents = ['Learn', 'Buy', 'Navigate', 'Compare'];
  
  return Array.from({ length: count }, (_, i) => ({
    mainKeyword,
    type: types[Math.floor(Math.random() * types.length)],
    keyword: `${mainKeyword} ${i % 2 === 0 ? 'best' : 'how to'} ${i + 1}`,
    msv: Math.floor(Math.random() * 10000),
    searchIntent: searchIntents[Math.floor(Math.random() * searchIntents.length)],
    kwDifficulty: Math.floor(Math.random() * 100),
    competition: parseFloat((Math.random() * 1).toFixed(2)),
    cpc: parseFloat((Math.random() * 10).toFixed(2)),
    answer: i % 3 === 0 ? 'Featured snippet available' : '',
    timestamp: new Date().toISOString(),
  }));
};

// Function to generate mock content matrix data
export const generateMockContentMatrix = (
  mainKeyword: string,
  count: number = 10
): ContentMatrixItem[] => {
  const clusters = ['Beginner', 'Intermediate', 'Advanced', 'Comparison', 'Review'];
  const contentTypes = ['Blog Post', 'Guide', 'Tutorial', 'Review', 'Comparison', 'List'];
  const categories = ['SEO', 'Content Marketing', 'Social Media', 'PPC', 'Email Marketing'];
  const statuses = ['Published', 'Draft', 'Planned', 'In Progress'];
  
  return Array.from({ length: count }, (_, i) => ({
    mainKeyword,
    cluster: clusters[Math.floor(Math.random() * clusters.length)],
    contentType: contentTypes[Math.floor(Math.random() * contentTypes.length)],
    focus: `${mainKeyword} ${i % 2 === 0 ? 'tips' : 'strategies'} ${i + 1}`,
    category: categories[Math.floor(Math.random() * categories.length)],
    url: `/blog/${mainKeyword.toLowerCase().replace(/\s+/g, '-')}-${i + 1}`,
    searchVol: Math.floor(Math.random() * 5000),
    keywords: `${mainKeyword}, ${mainKeyword} ${i % 2 === 0 ? 'tips' : 'strategies'}, ${
      i % 3 === 0 ? 'best' : 'top'
    } ${mainKeyword}`,
    status: statuses[Math.floor(Math.random() * statuses.length)],
  }));
};

// Function to generate complete mock webhook data
export const generateMockWebhookData = (
  mainKeyword: string,
  location: string = 'United States',
  language: string = 'English',
  limit: number = 10
): WebhookData => {
  return {
    mainKeyword,
    location,
    language,
    limit,
    date: new Date().toISOString().split('T')[0],
    timestamp: new Date().toISOString(),
    keywordResearch: generateMockKeywordResearch(mainKeyword, limit),
    contentMatrix: generateMockContentMatrix(mainKeyword, limit),
  };
};
